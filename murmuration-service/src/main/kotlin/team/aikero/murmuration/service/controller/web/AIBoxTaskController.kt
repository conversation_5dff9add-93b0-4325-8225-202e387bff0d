package team.aikero.murmuration.service.controller.web

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.util.Assert
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.ComplexPageParam
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.data.jimmer.findPage
import team.aikero.murmuration.common.req.task.ai_box.*
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.material.dto.AIBoxInfo
import team.aikero.murmuration.core.material.dto.AIBoxPage
import team.aikero.murmuration.core.material.dto.AIBoxPageReq
import team.aikero.murmuration.core.util.CodeGenerator
import team.aikero.murmuration.core.util.CodeRule
import team.aikero.murmuration.core.workflow.entity.AIBoxTask
import team.aikero.murmuration.core.workflow.task.TaskManager

@RestController
@RequestMapping("/web/ai-box/task")
class AIBoxTaskController(
    private val sql: KSqlClient,
    private val taskManager: TaskManager,
) {

    /**
     * 获取ai box任务分页
     */
    @PostMapping("/page")
    fun page(
        @RequestBody req: ComplexPageParam<AIBoxPageReq>
    ): DataResponse<PageVo<AIBoxPage>> =
        ok(
            sql.findPage(req, AIBoxPage::class)
        )

    /**
     * 获取ai box任务详细
     */
    @GetMapping("/{id}")
    fun getInfo(
        @PathVariable id: Long
    ): DataResponse<AIBoxInfo> =
        ok(
            sql.findOneById(AIBoxInfo::class, id)
        )

    /**
     * 创建手部修复任务
     */
    @PostMapping("/hand-repair")
    fun createHandRepairTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody req: List<HandRepairRequest>,
    ): DataResponse<List<Long>> =
        handleSimpleTaskRequests(taskSource, supplier, Ability.AI_BOX_HAND_REPAIR, req)

    /**
     * 创建智能剪头任务
     */
    @PostMapping("/smart-cutting-head")
    fun createSmartCuttingHeadTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<SmartCuttingHeadRequest>,
    ): DataResponse<List<Long>> =
        handleSimpleTaskRequests(taskSource, supplier, Ability.AI_BOX_SMART_CUTTING_HEAD, request)

    /**
     * 创建文字编辑任务
     */
    @PostMapping("/text-edit")
    fun createTextEditTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<TextEditRequest>,
    ): DataResponse<List<Long>> =
        handleSimpleTaskRequests(taskSource, supplier, Ability.AI_BOX_TEXT_EDIT, request)

    /**
     * 创建去水印任务
     */
    @PostMapping("/remove-watermark")
    fun createRemoveWatermarkTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<RemoveWatermarkRequest>,
    ): DataResponse<List<Long>> =
        handleCountBasedTaskRequests(taskSource, supplier, Ability.REMOVE_WATERMARK, request) { it.count }

    /**
     * 创建超分任务
     */
    @PostMapping("/four-k")
    fun createFourKTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<FourKRequest>,
    ): DataResponse<List<Long>> =
        handleCountBasedTaskRequests(taskSource, supplier, Ability.FOUR_K, request) { it.count }

    /**
     * 创建姿势裂变任务
     */
    @PostMapping("/posture")
    fun createPostureTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<PostureRequest>,
    ): DataResponse<List<Long>> =
        handleListBasedTaskRequests(taskSource, supplier, Ability.POSTURAL_FISSION, request) { req ->
            req.postureList.map { posture ->
                PostureFissionHandlerInputRequest(req.handlerRequest.imageUrl, posture)
            }
        }

    /**
     * 创建换背景任务
     */
    @PostMapping("/change-background")
    fun createChangeBackgroundTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<ChangeBackgroundRequest>,
    ): DataResponse<List<Long>> =
        handleListBasedTaskRequests(taskSource, supplier, Ability.CHANGE_BACKGROUND, request) { req ->
            req.scenePictureList.map { scenePicture ->
                ChangeBackgroundHandlerInputRequest(
                    req.handlerRequest.imageUrl,
                    req.handlerRequest.batchSize,
                    scenePicture
                )
            }
        }


    /**
     * 创建tryon任务
     */
    @PostMapping("/try-on")
    fun createTryOnTask(
        @RequestParam taskSource: TaskSource,
        @RequestParam supplier: Supplier,
        @RequestBody request: List<TryOnRequest>,
    ): DataResponse<List<Long>> =
        handleListBasedTaskRequests(taskSource, supplier, Ability.TRY_ON, request) { req ->
            val handlerRequest = req.handlerRequest
            val usingMj = handlerRequest.usingMj
            if (usingMj) {  //启用MJ（打开AI生成参考图）不需要参考图/背景图/模特图
                Assert.isTrue(handlerRequest.mjInfo.isNotNull(), "风格管理信息不能为空")
                listOf(
                    TryOnHandlerInputRequest(
                        clothImageUrl = handlerRequest.clothImageUrl,
                        styleInfo = handlerRequest.styleInfo,
                        usingMj = usingMj,
                        mjInfo = handlerRequest.mjInfo,
                        clothLength = handlerRequest.clothLength,
                        usingkontext = handlerRequest.usingkontext,
                        kontextBatchSize = handlerRequest.kontextBatchSize,
                        tryonBatchSize = handlerRequest.tryonBatchSize,
                        poseRange = handlerRequest.poseRange,
                    )
                )
            } else {
                Assert.isTrue(req.modelImageList.isNotEmpty(), "参考图不能为空")
                req.modelImageList!!.flatMap { modelImage ->
                    when {
                        req.backgroundImageList.isNotEmpty() && req.targetModelFaceImageList.isNotEmpty() -> {
                            req.backgroundImageList!!.flatMap { backgroundImage ->
                                req.targetModelFaceImageList!!.map { targetModelFaceImage ->
                                    TryOnHandlerInputRequest(
                                        modelImage = modelImage,
                                        clothImageUrl = handlerRequest.clothImageUrl,
                                        styleInfo = handlerRequest.styleInfo,
                                        usingMj = usingMj,
                                        clothLength = handlerRequest.clothLength,
                                        usingkontext = handlerRequest.usingkontext,
                                        targetModelFaceImage = targetModelFaceImage,//有模特图
                                        backgroundImage = backgroundImage,//有背景图
                                        kontextBatchSize = handlerRequest.kontextBatchSize,
                                        tryonBatchSize = handlerRequest.tryonBatchSize,
                                        poseRange = handlerRequest.poseRange,
                                    )
                                }
                            }
                        }
                        req.backgroundImageList.isNotEmpty() -> {
                            req.backgroundImageList!!.map { backgroundImage ->
                                TryOnHandlerInputRequest(
                                    modelImage = modelImage,
                                    clothImageUrl = handlerRequest.clothImageUrl,
                                    styleInfo = handlerRequest.styleInfo,
                                    usingMj = usingMj,
                                    clothLength = handlerRequest.clothLength,
                                    usingkontext = handlerRequest.usingkontext,
                                    backgroundImage = backgroundImage,//有背景图，无模特图
                                    kontextBatchSize = handlerRequest.kontextBatchSize,
                                    tryonBatchSize = handlerRequest.tryonBatchSize,
                                    poseRange = handlerRequest.poseRange,
                                )
                            }
                        }
                        req.targetModelFaceImageList.isNotEmpty() -> {
                            req.targetModelFaceImageList!!.map { targetModelFaceImage ->
                                TryOnHandlerInputRequest(
                                    modelImage = modelImage,
                                    clothImageUrl = handlerRequest.clothImageUrl,
                                    styleInfo = handlerRequest.styleInfo,
                                    usingMj = usingMj,
                                    clothLength = handlerRequest.clothLength,
                                    usingkontext = handlerRequest.usingkontext,
                                    targetModelFaceImage = targetModelFaceImage,//有模特图，无背景图
                                    kontextBatchSize = handlerRequest.kontextBatchSize,
                                    tryonBatchSize = handlerRequest.tryonBatchSize,
                                    poseRange = handlerRequest.poseRange,
                                )
                            }
                        }
                        else -> listOf(
                            TryOnHandlerInputRequest(
                                modelImage = modelImage,
                                clothImageUrl = handlerRequest.clothImageUrl,
                                styleInfo = handlerRequest.styleInfo,
                                usingMj = usingMj,
                                clothLength = handlerRequest.clothLength,
                                usingkontext = handlerRequest.usingkontext,
                                kontextBatchSize = handlerRequest.kontextBatchSize,
                                tryonBatchSize = handlerRequest.tryonBatchSize,
                                poseRange = handlerRequest.poseRange,
                            )
                        )
                    }
                }
            }
        }

    /**
     * 处理简单的任务请求（一对一）
     */
    private fun <T : TaskRequest<*>> handleSimpleTaskRequests(
        taskSource: TaskSource,
        supplier: Supplier,
        ability: Ability,
        requests: List<T>
    ): DataResponse<List<Long>> {
        val taskIds = mutableListOf<Long>()

        requests.forEach { request ->
            val aiBoxTaskId = createAIBoxTask(taskSource)
            createAndAddTask(request, supplier, ability, request.handlerRequest!!, aiBoxTaskId, taskSource, taskIds)
        }

        return ok(taskIds)
    }

    /**
     * 处理基于数量的任务请求（一对多，基于count）
     */
    private fun <T : TaskRequest<*>> handleCountBasedTaskRequests(
        taskSource: TaskSource,
        supplier: Supplier,
        ability: Ability,
        requests: List<T>,
        countExtractor: (T) -> Int
    ): DataResponse<List<Long>> {
        val taskIds = mutableListOf<Long>()

        requests.forEach { request ->
            val aiBoxTaskId = createAIBoxTask(taskSource)
            val count = countExtractor(request)

            repeat(count) {
                createAndAddTask(request, supplier, ability, request.handlerRequest!!, aiBoxTaskId, taskSource, taskIds)
            }
        }

        return ok(taskIds)
    }

    /**
     * 处理基于列表的任务请求（一对多，基于列表）
     */
    private fun <T : TaskRequest<*>> handleListBasedTaskRequests(
        taskSource: TaskSource,
        supplier: Supplier,
        ability: Ability,
        requests: List<T>,
        handlerRequestGenerator: (T) -> List<Any>
    ): DataResponse<List<Long>> {
        val taskIds = mutableListOf<Long>()

        requests.forEach { request ->
            val aiBoxTaskId = createAIBoxTask(taskSource)
            val handlerRequests = handlerRequestGenerator(request)

            handlerRequests.forEach { handlerRequest ->
                createAndAddTask(request, supplier, ability, handlerRequest, aiBoxTaskId, taskSource, taskIds)
            }
        }

        return ok(taskIds)
    }

    /**
     * 创建任务并添加到任务ID列表
     */
    private fun <T : TaskRequest<*>> createAndAddTask(
        request: T,
        supplier: Supplier,
        ability: Ability,
        handlerRequest: Any,
        aiBoxTaskId: Long,
        taskSource: TaskSource,
        taskIds: MutableList<Long>
    ) {
        if (taskSource != TaskSource.AI_BOX) {
            require(request.bizId != null) {
                "业务流过来的AI_BOX任务需要bizId"
            }
            require(request.bizType != null) {
                "业务流过来的AI_BOX任务需要bizType"
            }
        }
        taskManager.createTask(
            bizId = request.bizId,
            bizType = request.bizType,
            supplier = supplier,
            ability = ability,
            request = handlerRequest,
            aiBoxTaskId = aiBoxTaskId
        ).also(taskIds::add)
    }

    /**
     * 创建AIBox任务
     */
    private fun createAIBoxTask(taskSource: TaskSource): Long {
        return sql.save(
            AIBoxTask {
                this.taskCode = CodeGenerator.next(CodeRule.AI_BOX)
                this.taskSource = taskSource
            }, SaveMode.INSERT_ONLY
        ).modifiedEntity.id
    }
}