package team.aikero.murmuration.service.controller.inner

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.redisson.api.RedissonClient
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.murmuration.common.enums.SearchDimension
import team.aikero.murmuration.common.req.AddMuseSameStyleImageRequest
import team.aikero.murmuration.service.component.DictService
import team.aikero.murmuration.service.component.ImageVectorFetcher
import team.aikero.murmuration.service.component.SimilarityLevelMapper
import team.aikero.murmuration.service.repository.MuseSameStyleImageRepository
import team.aikero.murmuration.service.vector.Document
import team.aikero.murmuration.service.vector.VectorStoreFactory
import team.aikero.murmuration.service.vector.entity.MuseSameStyleImage
import team.aikero.murmuration.service.vector.entity.id
import team.aikero.murmuration.service.vector.entity.spuCode
import team.aikero.murmuration.util.checkValidUrl
import team.aikero.murmuration.util.execute

/**
 * MUSE同款图库内部服务
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@RestController
@RequestMapping("/inner/muse-same-style-image")
class MuseSameStyleImageInnerController(
    val sql: KSqlClient,
    val imageVectorFetcher: ImageVectorFetcher,
    val vectorStoreFactory: VectorStoreFactory,
    val similarityLevelMapper: SimilarityLevelMapper,
    val dictService: DictService,
    val museSameStyleImageRepository: MuseSameStyleImageRepository,
    val redisson: RedissonClient,
    val transactionTemplate: TransactionTemplate,
) {

    /**
     * 添加MUSE同款
     *
     * @param request 请求对象
     * @param checkSimilarity 是否进行相似度判定
     */
    @PostMapping("/add")
    @Transactional(rollbackFor = [Exception::class])
    fun add(
        @RequestBody request: AddMuseSameStyleImageRequest,
        @RequestParam checkSimilarity: Boolean,
    ): DataResponse<Unit> {
        // 相同的spuCode在同一时刻只会有一个成功
        val lockKey = "MUSE_SAME_STYLE_IMAGE_ADD:${request.spuCode}"
        return redisson.execute(lockKey, ok()) {
            // 校验输入
            validateRequest(request)

            // 相似度判定(去重)
            if (checkSimilarity && isSimilarToExistingData(request.url)) {
                return@execute DataResponse(
                    successful = false,
                    code = 701,
                    message = "图片[${request.url}]与图库中图片相似度过高",
                )
            }

            // 确保数据库操作和向量库操作在同一个事务中
            transactionTemplate.execute {
                // 插入或更新数据库
                val image = sql.save(MuseSameStyleImage {
                    this.spuCode = request.spuCode
                    this.supplyMode = request.supplyMode
                    this.categoryCode = request.categoryCode
                    this.url = request.url
                }).modifiedEntity

                // 插入或更新向量库
                upsertVectorStore(image)
            }

            ok()
        }
    }

    /**
     * 判断是否与已有数据相似度过高
     */
    private fun isSimilarToExistingData(url: String): Boolean {
        val vector = imageVectorFetcher.fetch(url, SearchDimension.MUSE_SAME_STYLE)
        val searchStore = vectorStoreFactory.getSearchStore(SearchDimension.MUSE_SAME_STYLE)
        val similarity = searchStore.top1Similarity(vector)
        val similarityLevel = similarityLevelMapper.mapSimilarityToLevel(similarity, SearchDimension.MUSE_SAME_STYLE)
        return similarityLevel > 1
    }

    /**
     * 插入或更新向量库
     */
    private fun upsertVectorStore(image: MuseSameStyleImage) {
        val vectorId = "${image.id}"
        val vector = imageVectorFetcher.fetch(image.url, SearchDimension.MUSE_SAME_STYLE)
        val searchStore = vectorStoreFactory.getSearchStore(SearchDimension.MUSE_SAME_STYLE)

        // 如果向量库中已经存在，则先删除
        searchStore.deleteById(vectorId)

        // 插入向量库
        val document = Document(id = vectorId, vector = vector)
        searchStore.insert(document)
    }

    /**
     * 删除MUSE同款
     *
     * @param spuCode SPU款号
     */
    @PostMapping("/delete")
    fun delete(@RequestParam spuCode: String): DataResponse<Unit> {
        val id = sql.createQuery(MuseSameStyleImage::class) {
            where(table.spuCode eq spuCode)
            select(table.id)
        }.fetchOneOrNull() ?: throw IllegalStateException("找不到SPU款号[${spuCode}]对应的数据")

        museSameStyleImageRepository.delete(id)
        return ok()
    }

    private fun validateRequest(request: AddMuseSameStyleImageRequest) {
        // 校验URL格式
        checkValidUrl(request.url) { "图片URL格式不正确" }
        // 校验字典编码
        dictService.checkDictCode("supply_mode", request.supplyMode)
    }
}
