package team.aikero.murmuration.service.node.task.aip.difyTryOn

import com.fasterxml.jackson.annotation.JsonProperty
import team.aikero.murmuration.common.enums.task.ClothingType
import team.aikero.murmuration.service.node.task.aip.*

@AipExecutorIdentifier(AipAbility.TRY_ON)
class DifyTryOnExecutor(client: AipClient): AipExecutor<DifyTryOnInput, DifyTryOnOutput>(client) {
    override fun validateTaskOutput(output: DifyTryOnOutput) {
        if (output.tryOnUrls.isNullOrEmpty()) {
            throw InvalidTaskOutputException("tryOnUrls 数组为空")
        }
    }
}

data class DifyTryOnInput(
    /**
     * 模特参考图
     */
    @field:JsonProperty("model_image_url")
    val modelImageUrl: String,

    /**
     * 商品衣服图
     */
    @field:JsonProperty("cloth_image_url")
    val clothImageUrl: String,

    /**
     * moodboard id,如--p m7306265713268752420
     */
    @field:JsonProperty("moodboard_id")
    val moodboardId: String? = null,

    /**
     * 启用MJ，true/false
     */
    @field:JsonProperty("using_mj")
    val usingMj: String = "false",

    @field:JsonProperty("addtional_cloth_image_url")
    val addtionalClothImageUrl: String? = null,

    /**
     * 字符串，可以有upper,lower,overall三种取值
     * 分别对应上半身，下半身，全身
     */
    @JsonProperty("cloth_length")
    val clothLength: ClothingType,

    /**
     * 启用换背景，true/false
     */
    @field:JsonProperty("using_change_background")
    val usingChangeBackground: String = "false",

    /**
     * 启用裂变，true/false
     */
    @field:JsonProperty("using_kontext")
    val usingkontext: String = "false",

    /**
     * 启用换脸，true/false
     */
    @field:JsonProperty("using_generate_swap_face")
    val usingGenerateSwapFace: String = "false",

    /**
     * 目标模特图
     */
    @field:JsonProperty("target_model_face_img_url")
    val targetModelFaceImgUrl: String? = null,

    /**
     * 生成图像比例，有裂变/换背景必传
     * @see AspectRatio
     */
    var aspectRatio: String? = null,

    /**
     * 背景图
     */
    @field:JsonProperty("background_image_url")
    val backgroundImageUrl: String? = null,

    /**
     * 姿势裂变生成张数
     */
    @field:JsonProperty("kontext_batch_size")
    val kontextBatchSize: Int? = null,

    /**
     * try on生成张数
     */
    @field:JsonProperty("tryon_batch_size")
    val tryonBatchSize: Int,

    /**
     * 姿势范围，多个字符串以\n的形式拼接
     */
    @field:JsonProperty("pose_range")
    val poseRange: String? = null,
)

data class DifyTryOnOutput(
    /**
     * mj生成图
     */
    @field:JsonProperty("mj_urls")
    val mjUrls: List<String>? = null,

    /**
     * try on生成图
     */
    @field:JsonProperty("try_on_urls")
    val tryOnUrls: List<String>? = null,

    /**
     * 换背景生成图
     */
    @field:JsonProperty("change_background_urls")
    val changeBackgroundUrls: List<String>? = null,

    /**
     * 姿势裂变生成图
     */
    @field:JsonProperty("kontext_urls")
    val kontextUrls: List<String>? = null,

    /**
     * 换脸生成图
     */
    @field:JsonProperty("swap_face_urls")
    val swapFaceUrls: List<String>? = null,
)