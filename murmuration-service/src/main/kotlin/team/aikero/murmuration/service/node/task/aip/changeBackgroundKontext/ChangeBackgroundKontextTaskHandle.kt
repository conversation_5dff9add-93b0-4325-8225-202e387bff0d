package team.aikero.murmuration.service.node.task.aip.changeBackgroundKontext

import team.aikero.blade.core.event.ErrorReportEvent
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.spring.Springs
import team.aikero.murmuration.common.req.task.ai_box.ChangeBackgroundHandlerInputRequest
import team.aikero.murmuration.common.vo.ScenePictureVo
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.TaskIdentifier
import team.aikero.murmuration.core.workflow.task.TaskHandler
import team.aikero.murmuration.core.workflow.task.TaskHandlerResult
import team.aikero.murmuration.core.workflow.task.TaskResult
import team.aikero.murmuration.service.utils.ImageUtil

/**
 * Kontext换背景任务处理器
 */
@TaskIdentifier(
    supplier = Supplier.AIP,
    ability = Ability.CHANGE_BACKGROUND
)
class ChangeBackgroundKontextTaskHandle(
    val executor: ChangeBackgroundKontextExecutor,
    private val imageUtil: ImageUtil
): TaskHandler<ChangeBackgroundHandlerInputRequest, String>  {
    override fun create(request: ChangeBackgroundHandlerInputRequest): String {
        return executor.createTask(
            ChangeBackgroundKontextInput(
                imageUrl =  request.imageUrl,
                aspectRatio =  imageUtil.getAspectRatio(request.imageUrl),
                backgroundImageUrl =  request.scenePicture.path,
                prompt =  request.scenePicture.caption,
                batchSize =  request.batchSize,
            )
        )
    }

    override fun query(request: ChangeBackgroundHandlerInputRequest, context: String): TaskResult<List<TaskHandlerResult>> {
        val taskId = context
        val taskResult = executor.getTask(taskId)
        return taskResult.map {
            if (it.backgroundPrompt.isNotBlank()) {
//                updateScenePictureCaption(request.scenePicture, it.backgroundPrompt!!)
            }
            val resultImageUrl = it.resImgs.first()
            listOf(TaskHandlerResult.image(resultImageUrl,it.backgroundPrompt))
        }
    }

    /**
     * 更新本地上传的背景图片的提示词
     */
    private fun updateScenePictureCaption(scenePicture: ScenePictureVo, caption: String) {
        try {
            //todo 调用bfg的sdk进行更新
        } catch (e: Exception) {
            log.error(e) { "发送更新背景图提示词失败: ${e.message}" }

            val reportEvent = ErrorReportEvent(
                source = "Kontext换背景-更新背景图提示词",
                ex = e,
                context = mapOf(
                    "背景图ID" to scenePicture.pictureId
                ),
            )
            Springs.publishEvent(reportEvent)
        }
    }
}