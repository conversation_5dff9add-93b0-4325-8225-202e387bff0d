package team.aikero.murmuration.common.req.task.ai_box

enum class TaskSource {
    /**
     * AI_BOX
     */
    AI_BOX,

    /**
     * AI满印
     */
    AI_FULL_SEAL,

    /**
     * 视觉中心
     */
    VISUAL_CENTER,

    /**
     * 商品中心
     */
    COMMODITY_CENTER
}

enum class CuttingSize {
    /**
     * 1:1
     */
    OneToOne,

    /**
     * 3:4
     */
    ThreeToFour;

    fun getSize(): String =
        when (this) {
            OneToOne -> "1000x1000"
            ThreeToFour -> "1340x1785"
        }

}

enum class TopLoc {
    /**
     * 下巴
     */
    JAW,

    /**
     * 鼻梁
     */
    BRIDGE_NOSE,

    /**
     * 鼻尖
     */
    TIP_NOSE,

    /**
     * 脖子
     */
    NECK
}