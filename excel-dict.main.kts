#!/usr/bin/env kotlin
@file:DependsOn("cn.idev.excel:fastexcel:1.2.0")

import cn.idev.excel.FastExcel
import cn.idev.excel.annotation.ExcelProperty
import cn.idev.excel.converters.Converter
import cn.idev.excel.enums.CellDataTypeEnum
import cn.idev.excel.metadata.GlobalConfiguration
import cn.idev.excel.metadata.data.ReadCellData
import cn.idev.excel.metadata.data.WriteCellData
import cn.idev.excel.metadata.property.ExcelContentProperty
import cn.idev.excel.write.handler.SheetWriteHandler
import cn.idev.excel.write.handler.context.SheetWriteHandlerContext
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.ss.util.CellRangeAddressList
import kotlin.io.path.createTempFile

/**
 * 字典提供者注册器
 */
object DictProviderRegistry {
    private val providers = mutableMapOf<String, DictProvider>()

    fun registerProvider(dictCode: String, provider: DictProvider) {
        providers[dictCode] = provider
    }

    fun getProvider(dictCode: String): DictProvider? = providers[dictCode]
}

/**
 * 字典提供者
 *
 * <AUTHOR>
 */
interface DictProvider {

    /**
     * 获取字典映射关系
     */
    fun mapping(code: String): Map<String, String>

}

/**
 * 内存字典提供者
 *
 * <AUTHOR>
 */
class MemoryDictProvider(val dict: Map<String, Map<String, String>>) : DictProvider {

    override fun mapping(code: String): Map<String, String> {
        return dict[code] ?: emptyMap()
    }
}

/**
 * 枚举字典提供者
 *
 * 将枚举类转换为字典映射，支持以下特性：
 * 1. 使用枚举常量名称作为键
 * 2. 优先使用 KDoc 注释作为显示值，否则使用枚举名称
 * 3. 支持任意枚举类型的自动转换
 * 4. 提供完整的错误处理和验证
 *
 * @param enum 枚举类的 Class 对象
 * <AUTHOR>
 */
class EnumDictProvider(val enum: Class<*>) : DictProvider {

    /**
     * 获取字典映射关系
     *
     * @param code 字典编码（对于枚举字典提供者，此参数被忽略）
     * @return 枚举常量名称到显示值的映射
     * @throws IllegalArgumentException 当枚举类无效时
     */
    override fun mapping(code: String): Map<String, String> {
        return try {
            extractEnumMapping()
        } catch (e: Exception) {
            throw IllegalArgumentException("无法从枚举类 [${enum.simpleName}] 提取字典映射: ${e.message}", e)
        }
    }

    /**
     * 提取枚举映射关系
     *
     * @return 枚举常量名称到显示值的映射
     */
    private fun extractEnumMapping(): Map<String, String> {
        // 验证枚举类
        if (!enum.isEnum) {
            throw IllegalArgumentException("提供的类 [${enum.name}] 不是枚举类型")
        }

        val enumConstants = enum.enumConstants
        if (enumConstants.isNullOrEmpty()) {
            return emptyMap()
        }

        return enumConstants.associate { enumConstant ->
            val enumValue = enumConstant as Enum<*>
            val name = enumValue.name
            val displayValue = extractDisplayValue(enumValue)
            name to displayValue
        }
    }

    /**
     * 提取枚举常量的显示值
     *
     * 优先级：
     * 1. KDoc 注释内容（去除前后空白）
     * 2. 枚举常量名称
     *
     * @param enumConstant 枚举常量
     * @return 显示值
     */
    private fun extractDisplayValue(enumConstant: Enum<*>): String {
        return try {
            // 尝试获取 KDoc 注释
            val field = enum.getField(enumConstant.name)
            val kdocComment = extractKDocComment(field)

            if (kdocComment.isNotBlank()) {
                kdocComment
            } else {
                // 回退到枚举名称
                enumConstant.name
            }
        } catch (e: Exception) {
            // 如果获取注释失败，使用枚举名称
            enumConstant.name
        }
    }

    /**
     * 从字段中提取显示值
     *
     * 支持多种方式获取显示值：
     * 1. @JsonProperty 注解的 value 属性
     * 2. 其他自定义显示值注解（可扩展）
     * 3. KDoc 注释（运行时通常不可用）
     *
     * @param field 枚举字段
     * @return 显示值，如果不可用则返回空字符串
     */
    private fun extractKDocComment(field: java.lang.reflect.Field): String {
        // 1. 检查 @JsonProperty 注解
        try {
            val jsonPropertyClass = Class.forName("com.fasterxml.jackson.annotation.JsonProperty")
            val jsonProperty = field.getAnnotation(jsonPropertyClass as Class<Annotation>)
            if (jsonProperty != null) {
                val valueMethod = jsonPropertyClass.getMethod("value")
                val value = valueMethod.invoke(jsonProperty) as String
                if (value.isNotBlank()) {
                    return value
                }
            }
        } catch (e: Exception) {
            // JsonProperty 注解不可用，继续尝试其他方式
        }

        // 2. 可以在这里添加对其他注解的支持
        // 例如：@DisplayName、@Description、@Label 等

        // 3. 检查其他可能的显示值注解
        field.annotations.forEach { annotation ->
            when (annotation.annotationClass.simpleName) {
                "DisplayName" -> {
                    try {
                        val valueMethod = annotation.annotationClass.java.getMethod("value")
                        val value = valueMethod.invoke(annotation) as String
                        if (value.isNotBlank()) return value
                    } catch (e: Exception) { /* 忽略 */ }
                }
                "Description" -> {
                    try {
                        val valueMethod = annotation.annotationClass.java.getMethod("value")
                        val value = valueMethod.invoke(annotation) as String
                        if (value.isNotBlank()) return value
                    } catch (e: Exception) { /* 忽略 */ }
                }
            }
        }

        // 4. 在标准的 JVM 运行时环境中，KDoc 注释信息通常不可用
        // 这里为未来扩展预留

        return ""
    }
}

/**
 * Map 字典提供者
 *
 * <AUTHOR>
 */
class MapDictProvider(val dict: Map<String, String>) : DictProvider {

    override fun mapping(code: String): Map<String, String> {
        return dict
    }
}

/**
 * 字典格式化
 *
 * <AUTHOR>
 */
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class DictFormat(
    /**
     * 字典编码
     */
    val value: String,
    /**
     * 下拉配置
     */
    val range: Range = Range()
)

/**
 * 字典下拉行范围
 */
annotation class Range(
    /**
     * 起始行
     */
    val start: Int = 1,
    /**
     * 结束行
     */
    val end: Int = 10
)

/**
 * 字典转换器 for FastExcel
 *
 * <AUTHOR>
 */
class DictConverter : Converter<String> {

    override fun supportJavaTypeKey(): Class<String> {
        return String::class.java
    }

    override fun supportExcelTypeKey(): CellDataTypeEnum {
        return CellDataTypeEnum.STRING
    }

    override fun convertToJavaData(
        cellData: ReadCellData<*>,
        contentProperty: ExcelContentProperty?,
        globalConfiguration: GlobalConfiguration?
    ): String? {
        try {
            // 从Excel单元格中获取原始字符串值
            val cellValue = when (cellData.type) {
                CellDataTypeEnum.STRING -> cellData.stringValue
                CellDataTypeEnum.NUMBER -> cellData.numberValue?.toString()
                CellDataTypeEnum.BOOLEAN -> cellData.booleanValue?.toString()
                else -> cellData.stringValue
            }

            if (cellValue.isNullOrBlank()) {
                return null
            }

            // 从content属性获取字段信息
            val field = contentProperty?.field
            if (field != null) {
                val dictFormat = field.getAnnotation(DictFormat::class.java)
                if (dictFormat != null) {
                    // 使用字典映射将显示值转换为实际值
                    return convertFromDisplayValue(cellValue, dictFormat)
                }
            }

            // 如果不需要字典转换，则返回原始值
            return cellValue

        } catch (e: Exception) {
            throw RuntimeException("Failed to convert Excel cell data to Java value: ${e.message}", e)
        }
    }

    /**
     * Convert Java object value to Excel cell data
     * This method is called when writing to Excel
     */
    override fun convertToExcelData(
        value: String?,
        contentProperty: ExcelContentProperty?,
        globalConfiguration: GlobalConfiguration?
    ): WriteCellData<*> {
        try {
            if (value == null) {
                return WriteCellData<String>()
            }

            // Get field information from content property
            val field = contentProperty?.field
            if (field != null) {
                val dictFormat = field.getAnnotation(DictFormat::class.java)
                if (dictFormat != null) {
                    // Convert actual value to display value using reverse dictionary mapping
                    val displayValue = convertToDisplayValue(value, dictFormat)
                    return WriteCellData<String>(displayValue)
                }
            }

            // If no dictionary conversion needed, return original value
            return WriteCellData<String>(value)

        } catch (e: Exception) {
            throw RuntimeException("Failed to convert Java value to Excel cell data: ${e.message}", e)
        }
    }

    /**
     * Convert display value (from Excel) to actual value using dictionary mapping
     *
     * @param displayValue The display value from Excel (e.g., "男", "启用")
     * @param dictFormat The dictionary conversion annotation
     * @return The actual value (e.g., "M", "1")
     */
    private fun convertFromDisplayValue(displayValue: String, dictFormat: DictFormat): String {
        try {
            val provider = DictProviderRegistry.getProvider(dictFormat.value)
                ?: throw IllegalArgumentException("No provider found for dict type: ${dictFormat.value}")

            val mapping = provider.mapping(dictFormat.value)
            val actualValue = mapping[displayValue]

            // If no mapping found, check if the value is already an actual value
            if (actualValue == null) {
                val reverseMapping = mapping.values
                if (reverseMapping.contains(displayValue)) {
                    // The input is already an actual value, return as-is
                    return displayValue
                }
                // Log warning for unmapped value but don't fail
                println("Warning: No mapping found for display value '$displayValue' in dict type '${dictFormat.value}', using original value")
                return displayValue
            }

            return actualValue
        } catch (e: Exception) {
            println("Error converting display value '$displayValue' for dict type '${dictFormat.value}': ${e.message}")
            return displayValue
        }
    }

    /**
     * Convert actual value to display value (for Excel) using reverse dictionary mapping
     *
     * @param actualValue The actual value (e.g., "M", "1")
     * @param dictFormat The dictionary conversion annotation
     * @return The display value (e.g., "男", "启用")
     */
    private fun convertToDisplayValue(actualValue: String, dictFormat: DictFormat): String {
        try {
            val provider = DictProviderRegistry.getProvider(dictFormat.value)
                ?: throw IllegalArgumentException("No provider found for dict type: ${dictFormat.value}")

            val reverseMapping = provider.mapping(dictFormat.value).map { it.value to it.key }.toMap()

            // Try different type conversions for the key lookup
            val displayValue = reverseMapping[actualValue]

            // If still no reverse mapping found, check if the value is already a display value
            if (displayValue == null) {
                val mapping = provider.mapping(dictFormat.value)
                if (mapping.containsKey(actualValue)) {
                    // The input is already a display value, return as-is
                    return actualValue
                }
                // Log warning for unmapped value but don't fail
                println("Warning: No reverse mapping found for actual value '$actualValue' in dict type '${dictFormat.value}', using original value")
                return actualValue
            }

            return displayValue
        } catch (e: Exception) {
            println("Error converting actual value '$actualValue' for dict type '${dictFormat.value}': ${e.message}")
            return actualValue
        }
    }
}

/**
 * Enhanced Demo Data Class with Dictionary Annotations
 */
data class DemoData(
    @ExcelProperty("姓名")
    var name: String,

    @ExcelProperty("年龄")
    var age: Int,

    @DictFormat(value = "gender")
    @ExcelProperty("性别")
    var gender: String,

    @DictFormat(value = "status")
    @ExcelProperty("状态")
    var status: String,

    @ExcelProperty("部门")
    var department: String,

    @ExcelProperty("备注")
    var remark: String,

    @DictFormat(value = "userStatus")
    @ExcelProperty("用户状态")
    var userStatus: String  // 使用字符串存储枚举值，通过字典转换显示
)

/**
 * 自动列宽处理器
 *
 * <AUTHOR>
 */
class AutoWidthWriteHandler : SheetWriteHandler {

    override fun afterSheetCreate(context: SheetWriteHandlerContext) {
        val sheet = context.writeSheetHolder.sheet
        val dataClass = context.writeSheetHolder.clazz

        dataClass?.declaredFields?.forEachIndexed { index, field ->
            val excelProperty = field.getAnnotation(ExcelProperty::class.java)
            val columnWidth = calculateOptimalWidth(field.name, excelProperty?.value?.firstOrNull())
            sheet.setColumnWidth(index, columnWidth)
        }
    }

    private fun calculateOptimalWidth(fieldName: String, headerName: String?): Int {
        val displayName = headerName ?: fieldName

        val baseWidth = maxOf(2, minOf(20, displayName.length)) + 5
        // OOXML 规范一个字符 255个单位，最大 256字符，此处简化使用，默认一个文字占用两个字符
        return baseWidth * 512
    }
}

/**
 * 字典下拉处理器
 *
 * 自动解析字典注解，生成下拉选项
 *
 * <AUTHOR>
 */
class DictDropdownWriteHandler : SheetWriteHandler {

    override fun afterSheetCreate(context: SheetWriteHandlerContext) {
        val sheet = context.writeSheetHolder.sheet
        val dataClass = context.writeSheetHolder.clazz

        if (dataClass != null) {
            createDropdownValidations(sheet, dataClass)
        }
    }

    private fun createDropdownValidations(sheet: Sheet, dataClass: Class<*>) {
        dataClass.declaredFields.forEachIndexed { index, field ->
            val dictFormat = field.getAnnotation(DictFormat::class.java)
            if (dictFormat != null) {
                createDropdownForField(sheet, index, dictFormat)
            }
        }
    }

    private fun createDropdownForField(sheet: Sheet, columnIndex: Int, dictFormat: DictFormat) {
        val provider = DictProviderRegistry.getProvider(dictFormat.value)
        if (provider != null) {
            val options = provider.mapping(dictFormat.value).values
            if (options.isNotEmpty()) {
                val config = dictFormat.range

                // Create cell range for validation
                val range = CellRangeAddressList(config.start, config.end, columnIndex, columnIndex)

                // Create validation constraint
                val helper = sheet.dataValidationHelper
                val constraint = helper.createExplicitListConstraint(options.toTypedArray())
                val validation = helper.createValidation(constraint, range)

                // Configure validation
                validation.showErrorBox = true
                validation.emptyCellAllowed = false
                validation.createErrorBox("输入错误", "请从下拉列表中选择有效值")
                validation.showPromptBox = true
                validation.createPromptBox("提示", "请选择: ${options.joinToString(", ")}")

                // Add validation to sheet
                sheet.addValidationData(validation)
            }
        }
    }
}


/**
 * 自定义显示名称注解
 */
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class DisplayName(val value: String)


/**
 * 示例枚举 - 用户状态
 */
enum class UserStatus {
    /**
     * 活跃用户
     */
    @DisplayName("活跃")
    ACTIVE,

    /**
     * 非活跃用户
     */
    @DisplayName("非活跃")
    INACTIVE,

    /**
     * 已禁用
     */
    @DisplayName("已禁用")
    DISABLED

}


val fileName = "simpleWrite" + System.currentTimeMillis() + ".xlsx"
val createTempFile = createTempFile(suffix = fileName)

// Create test data with actual dictionary values (these will be converted to display values in Excel)
val data = (1..10).map { index ->
    val genderValues = listOf("M", "F", "U")
    val statusValues = listOf("1", "0", "2")  // String representation of status values
    val userStatusValues = listOf("ACTIVE", "INACTIVE", "DISABLED", "PENDING")  // 枚举名称

    DemoData(
        name = "用户$index",
        age = 20 + index,
        gender = genderValues[index % 3],  // Will be converted to "男", "女", "未知"
        status = statusValues[index % 3],  // Will be converted to "启用", "禁用", "待审核"
        department = "技术部",
        remark = "备注信息$index",
        userStatus = userStatusValues[index % 4]  // Will be converted using EnumDictProvider
    )
}


DictProviderRegistry.registerProvider("gender", MapDictProvider(mapOf("男" to "M", "女" to "F", "未知" to "U")))
DictProviderRegistry.registerProvider("status", MapDictProvider(mapOf("启用" to "1", "禁用" to "0", "待审核" to "2")))

// 注册枚举字典提供者
DictProviderRegistry.registerProvider("userStatus", EnumDictProvider(UserStatus::class.java))

FastExcel.write(createTempFile.toFile(), DemoData::class.java)
    .sheet("Sheet1")
    .registerWriteHandler(AutoWidthWriteHandler())
    .registerWriteHandler(DictDropdownWriteHandler())
    .registerConverter(DictConverter())
    .doWrite(data)

println(createTempFile)
Runtime.getRuntime().exec("open $createTempFile")
