export team.aikero.murmuration.core.workflow.entity.AIBoxTask
    -> package team.aikero.murmuration.core.material.dto

specification AIBoxPageReq {
    like(taskCode)
    creatorId
    status
    taskSource
    flat(taskInstances){
        ability as ability
    }
    ge(createdTime)
    le(createdTime)
}

AIBoxPage {
    id
    taskCode
    status
    taskInstances {
        id
        ability
        detail {
            id
            request
        }
        results {
            id
            url
        }
    }
    creatorId
    creatorName
    createdTime
    finishTime
}

AIBoxInfo {
    id
    taskCode
    creatorId
    creatorName
    createdTime
    status
    finishTime
    taskInstances {
        id
        ability
        detail {
            id
            request
            context
        }
        results {
            id
            url
        }
    }
}