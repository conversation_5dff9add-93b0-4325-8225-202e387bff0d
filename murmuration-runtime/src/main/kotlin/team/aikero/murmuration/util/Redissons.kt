package team.aikero.murmuration.util

import org.redisson.api.RedissonClient

/**
 * 互斥操作
 */
fun RedissonClient.execute(key: String, block: () -> Unit) = execute(key, Unit, block)

/**
 * 带返回值的互斥操作
 */
fun <R> RedissonClient.execute(key: String, defaultValue: R, block: () -> R): R {
    val lock = this.getLock(key)
    if (!lock.tryLock()) return defaultValue

    return try {
        block()
    } finally {
        lock.unlock()
    }
}
