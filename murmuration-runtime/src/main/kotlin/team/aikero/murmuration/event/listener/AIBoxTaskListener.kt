package team.aikero.murmuration.event.listener

import com.fasterxml.jackson.databind.ObjectMapper
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.redisson.api.RedissonClient
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import team.aikero.blade.auth.withSystemUser
import team.aikero.murmuration.common.req.task.ai_box.TaskSource
import team.aikero.murmuration.core.workflow.entity.*
import team.aikero.murmuration.core.workflow.event.TaskCompletedEvent
import team.aikero.murmuration.core.workflow.event.TaskPrepareEvent
import team.aikero.murmuration.infra.rocketmq.Message
import team.aikero.murmuration.infra.rocketmq.RocketMQClient
import team.aikero.murmuration.infra.rocketmq.TopicHolder
import java.time.LocalDateTime

@Component
class AIBoxTaskListener(
    val sql: KSqlClient,
    val objectMapper: ObjectMapper,
    val redisson: RedissonClient,
    val rocketMQClient: RocketMQClient,
) {

    @EventListener
    fun onTaskRunning(event: TaskPrepareEvent) = withSystemUser {
        val task = event.task
        val taskInstance = sql.findOneById(TASK_INSTANCE_FETCHER, task.id)

        // 只处理触发来源为API调用的任务
        if (taskInstance.triggerSource != TriggerSource.AI_BOX) {
            return@withSystemUser
        }

        // 状态检查
        if (taskInstance.status != TaskStatus.PREPARED) {
            throw IllegalStateException("任务实例[${taskInstance.id}]状态异常: 预期为[${TaskStatus.COMPLETED}], 实际为[${taskInstance.status}]")
        }

        val aiBoxTask = taskInstance.aiBoxTask!!

        sql.save(
            AIBoxTask {
                this.id = aiBoxTask.id
                this.finishTime = LocalDateTime.now()
                this.status = TaskStatus.RUNNING
            }, SaveMode.UPDATE_ONLY
        )

    }

    /**
     * 任务完成事件
     */
    @EventListener
    fun onTaskCompleted(event: TaskCompletedEvent) = withSystemUser {
        val task = event.task
        val taskInstance = sql.findOneById(TASK_INSTANCE_FETCHER, task.id)

        // 只处理触发来源为API调用的任务
        if (taskInstance.triggerSource != TriggerSource.AI_BOX) {
            return@withSystemUser
        }

        // 状态检查
        if (taskInstance.status != TaskStatus.COMPLETED) {
            throw IllegalStateException("任务实例[${taskInstance.id}]状态异常: 预期为[${TaskStatus.COMPLETED}], 实际为[${taskInstance.status}]")
        }

        // 只有 AiBoxTask 下的所有任务完成后，才更新 AiBoxTask 本身的状态
        // 由于多个任务可能同时完成，因此需要加锁
        val aiBoxTask = taskInstance.aiBoxTask!!

        if (aiBoxTask.taskSource != TaskSource.AI_BOX) {
            val messageBody = TaskStatusNotification(
                taskId = taskInstance.id,
                bizId = taskInstance.bizId
                    ?: throw NullPointerException("当前任务实例[${taskInstance.id}]触发来源为API调用, 但业务ID为空"),
                bizType = taskInstance.bizType
                    ?: throw NullPointerException("当前任务实例[${taskInstance.id}]触发来源为API调用, 但业务类型为空"),
                status = taskInstance.status,
                results = taskInstance.results.map {
                    MQTaskResult(url = it.url)
                }
            )

            val message = Message(
                topic = TopicHolder.topic,
                keys = listOf("${taskInstance.id}"),
                tag = "${task.ability}:${task.supplier}",
                payload = messageBody,
            )
            rocketMQClient.send(message)
        }

        val lock = redisson.getLock("AiBoxTaskListener:${aiBoxTask.id}")
        lock.lock()

        try {
            // 有一个执行成功且没有生成中的任务为AIBox成功
            val asCompleted = aiBoxTask.taskInstances.any { it.status == TaskStatus.COMPLETED } &&
                    aiBoxTask.taskInstances.any { it.status != TaskStatus.RUNNING }
            // 全部失败才为失败
            val asFailed = aiBoxTask.taskInstances.all { it.status == TaskStatus.FAILED }

            val entity = AIBoxTask {
                this.id = aiBoxTask.id
                this.finishTime = LocalDateTime.now()
            }
            if (asCompleted) {
                sql.save(entity.copy {
                    this.status = TaskStatus.COMPLETED
                }, SaveMode.UPDATE_ONLY)
            }
            if (asFailed) {
                sql.save(entity.copy {
                    this.status = TaskStatus.FAILED
                }, SaveMode.UPDATE_ONLY)
            }
        } finally {
            lock.unlock()
        }
    }

    companion object {
        private val TASK_INSTANCE_FETCHER = newFetcher(TaskInstance::class).by {
            triggerSource()
            bizId()
            bizType()
            status()
            results {
                url()
            }
            aiBoxTask {
                taskSource()
                taskInstances {
                    status()
                }
            }
        }
    }
}
